{"name": "@shikijs/core", "type": "module", "version": "3.11.0", "description": "Core of <PERSON><PERSON>", "author": "<PERSON> <<EMAIL>>; <PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/shikijs/shiki#readme", "repository": {"type": "git", "url": "git+https://github.com/shikijs/shiki.git", "directory": "packages/core"}, "bugs": "https://github.com/shikijs/shiki/issues", "keywords": ["shiki"], "sideEffects": false, "exports": {".": "./dist/index.mjs", "./textmate": {"types": "./dist/textmate.d.mts", "default": "./dist/textmate.mjs"}, "./types": {"types": "./dist/types.d.mts"}, "./dist/*": "./dist/*", "./package.json": "./package.json", "./*": "./dist/*"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist"], "dependencies": {"@shikijs/vscode-textmate": "^10.0.2", "@types/hast": "^3.0.4", "hast-util-to-html": "^9.0.5", "@shikijs/types": "3.11.0"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "test": "vitest"}}
{"name": "@shikijs/engine-javascript", "type": "module", "version": "3.11.0", "description": "Engine for Shiki using JavaScript's native RegExp", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/shikijs/shiki#readme", "repository": {"type": "git", "url": "git+https://github.com/shikijs/shiki.git", "directory": "packages/engine-javascript"}, "bugs": "https://github.com/shikijs/shiki/issues", "keywords": ["shiki", "shiki-engine"], "sideEffects": false, "exports": {".": "./dist/index.mjs", "./raw": "./dist/engine-raw.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist"], "dependencies": {"@shikijs/vscode-textmate": "^10.0.2", "oniguruma-to-es": "^4.3.3", "@shikijs/types": "3.11.0"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub"}}